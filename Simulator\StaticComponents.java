package Simulator;

import fr.emse.fayol.maqit.simulator.components.SituatedComponent;
import fr.emse.fayol.maqit.simulator.components.ColorTransitZone;
import fr.emse.fayol.maqit.simulator.components.ColorStartZone;
import fr.emse.fayol.maqit.simulator.components.ColorExitZone;
import fr.emse.fayol.maqit.simulator.components.ColorObstacle;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;

import java.util.ArrayList;
import java.util.List;

/**
 * Auteurs: Oussama GUELFAA & Houssam ALLALI
 * Date: 25-05-2025
 * 
 * Composants statiques simplifiés pour le projet étudiant.
 * Ces composants ne peuvent pas bouger pendant la simulation.
 */

/**
 * Classe de base simple pour les zones qui ne bougent pas
 */
abstract class ZoneStatique extends SituatedComponent {

    public ZoneStatique(int[] position) {
        super(position);
        LogManager.getInstance().logAction("ZoneStatique",
            "Zone créée à [" + position[0] + "," + position[1] + "]");
    }

    // Empêcher le déplacement en ne faisant rien
    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones statiques ne bougent pas
        LogManager.getInstance().logError("ZoneStatique",
            "Tentative de déplacement d'une zone statique empêchée");
    }
}

/**
 * Zone de transit qui ne peut pas bouger
 */
class StaticTransitZone extends ColorTransitZone {

    public StaticTransitZone(int[] position, int[] couleur, int capacite) {
        super(position, couleur, capacite);
        LogManager.getInstance().logAction("StaticTransitZone",
            "Zone de transit créée à [" + position[0] + "," + position[1] + "] capacité " + capacite);
    }

    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones de transit ne bougent pas
        LogManager.getInstance().logError("StaticTransitZone",
            "Tentative de déplacement d'une zone de transit empêchée");
    }
}

/**
 * Zone de départ qui ne peut pas bouger
 */
class StaticStartZone extends ColorStartZone {

    public StaticStartZone(int[] position, int[] couleur) {
        super(position, couleur);
        LogManager.getInstance().logAction("StaticStartZone",
            "Zone de départ créée à [" + position[0] + "," + position[1] + "]");
    }

    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones de départ ne bougent pas
        LogManager.getInstance().logError("StaticStartZone",
            "Tentative de déplacement d'une zone de départ empêchée");
    }
}

/**
 * Zone de sortie qui ne peut pas bouger
 */
class StaticExitZone extends ColorExitZone {

    public StaticExitZone(int[] position, int[] couleur) {
        super(position, couleur);
        LogManager.getInstance().logAction("StaticExitZone",
            "Zone de sortie créée à [" + position[0] + "," + position[1] + "]");
    }

    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les zones de sortie ne bougent pas
        LogManager.getInstance().logError("StaticExitZone",
            "Tentative de déplacement d'une zone de sortie empêchée");
    }
}

/**
 * Obstacle qui ne peut pas bouger
 */
class StaticObstacle extends ColorObstacle {

    public StaticObstacle(int[] position, int[] couleur) {
        super(position, couleur);
        LogManager.getInstance().logAction("StaticObstacle",
            "Obstacle créé à [" + position[0] + "," + position[1] + "]");
    }

    @Override
    public void setLocation(int[] nouvellePosition) {
        // Ne rien faire - les obstacles ne bougent pas
        LogManager.getInstance().logError("StaticObstacle",
            "Tentative de déplacement d'un obstacle empêchée");
    }
}

/**
 * Gestionnaire simple pour les composants statiques
 * Version simplifiée pour étudiants - pas de pattern Singleton complexe
 */
class StaticComponentManager {

    // Instance unique simple
    private static StaticComponentManager instance;
    
    // Liste simple des composants statiques
    private List<SituatedComponent> composantsStatiques = new ArrayList<>();
    
    // Environnement de simulation
    private ColorGridEnvironment environnement;

    // Constructeur simple
    private StaticComponentManager() {
        LogManager.getInstance().logAction("StaticComponentManager", "Gestionnaire créé");
    }

    // Obtenir l'instance unique
    public static StaticComponentManager getInstance() {
        if (instance == null) {
            instance = new StaticComponentManager();
        }
        return instance;
    }

    // Définir l'environnement
    public void setEnvironment(ColorGridEnvironment env) {
        this.environnement = env;
        LogManager.getInstance().logAction("StaticComponentManager", "Environnement associé");
    }

    // Enregistrer un composant statique
    public void registerStaticComponent(SituatedComponent composant) {
        composantsStatiques.add(composant);
        LogManager.getInstance().logAction("StaticComponentManager",
            "Composant statique enregistré à [" + composant.getLocation()[0] + "," + 
            composant.getLocation()[1] + "]");
    }

    // Vérifier si un composant est statique (méthode simple)
    public boolean isStaticComponent(SituatedComponent composant) {
        if (composant == null) return false;
        
        // Vérifier si c'est dans la liste OU si c'est une instance d'une classe statique
        return composantsStatiques.contains(composant) ||
               composant instanceof StaticTransitZone ||
               composant instanceof StaticStartZone ||
               composant instanceof StaticExitZone ||
               composant instanceof StaticObstacle;
    }

    // Vérifier si un composant peut bouger
    public boolean canMove(SituatedComponent composant) {
        return !isStaticComponent(composant);
    }
}
