package Simulator;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Auteurs: Oussama GUELFAA, Houssam ALLALI & Noa AKAYAD
 * Date: 25-05-2025
 *
 * Notre système de logs pour voir ce qui se passe dans la simulation.
 * On a mis des couleurs pour que ce soit plus joli et plus facile à lire !
 * Chaque type de message a sa couleur (actions en vert, erreurs en rouge, etc.)
 */
public class LogManager {

    private static LogManager instance;  // Une seule instance pour toute la simulation

    // Codes couleurs pour rendre la console plus jolie
    public static final String RESET = "\u001B[0m";    // Retour à la couleur normale
    public static final String GREEN = "\u001B[32m";   // Vert pour les actions
    public static final String YELLOW = "\u001B[33m";  // Jaune pour la batterie
    public static final String BLUE = "\u001B[34m";    // Bleu pour les zones de transit
    public static final String CYAN = "\u001B[36m";    // Cyan pour la coordination
    public static final String PURPLE = "\u001B[35m";  // Violet pour les livraisons
    public static final String RED = "\u001B[31m";     // Rouge pour les erreurs
    public static final String BOLD = "\u001B[1m";     // Texte en gras

    // Les différents types de messages qu'on peut afficher
    public enum MessageType {
        ACTION(GREEN, "ACTION"),           // Quand un robot fait quelque chose
        BATTERY(YELLOW, "BATTERIE"),       // Tout ce qui concerne la batterie
        TRANSIT(BLUE, "TRANSIT"),          // Utilisation des zones de transit
        COORDINATION(CYAN, "COORDINATION"), // Communication entre robots
        DELIVERY(PURPLE, "LIVRAISON"),     // Livraisons de colis
        ERROR(RED, "ERREUR");              // Quand ça va pas

        private final String color;
        private final String name;

        MessageType(String color, String name) {
            this.color = color;
            this.name = name;
        }

        public String getColor() { return color; }
        public String getName() { return name; }
    }

    // Configuration simple
    private boolean verbose = true;
    private boolean useColors = true;

    // Compteurs simples
    private Map<MessageType, Integer> globalCounts = new HashMap<>();
    private Map<String, Map<MessageType, Integer>> robotCounts = new HashMap<>();

    // Format pour l'heure
    private SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss.SSS");

    private LogManager() {
        // Initialiser les compteurs
        for (MessageType type : MessageType.values()) {
            globalCounts.put(type, 0);
        }
        System.out.println(BOLD + "Système de logs initialisé" + RESET);
    }

    public static LogManager getInstance() {
        if (instance == null) {
            instance = new LogManager();
        }
        return instance;
    }

    public void setVerboseMode(boolean verbose) {
        this.verbose = verbose;
    }

    public void setUseColors(boolean useColors) {
        this.useColors = useColors;
    }

    // Méthode principale pour enregistrer un message
    private void logMessage(String robotName, MessageType type, String message) {
        // Mettre à jour les compteurs globaux
        globalCounts.put(type, globalCounts.get(type) + 1);

        // Mettre à jour les compteurs par robot
        if (!robotCounts.containsKey(robotName)) {
            robotCounts.put(robotName, new HashMap<>());
            for (MessageType t : MessageType.values()) {
                robotCounts.get(robotName).put(t, 0);
            }
        }
        Map<MessageType, Integer> robotStats = robotCounts.get(robotName);
        robotStats.put(type, robotStats.get(type) + 1);

        // Vérifier si on doit afficher le message
        if (!verbose && type != MessageType.ERROR) {
            return;
        }

        // Formater le message
        String time = timeFormat.format(new Date());
        String finalMessage = String.format("[%s] [%s] [%s] %s",
                time, type.getName(), robotName, message);

        // Ajouter les couleurs si activées
        if (useColors) {
            finalMessage = type.getColor() + finalMessage + RESET;
        }

        // Afficher le message
        System.out.println(finalMessage);
    }

    // Méthodes publiques pour chaque type de message
    public void logAction(String robotName, String message) {
        logMessage(robotName, MessageType.ACTION, message);
    }

    public void logBattery(String robotName, double batteryLevel, String message) {
        String fullMessage = String.format("Niveau: %.1f%% - %s", batteryLevel, message);
        logMessage(robotName, MessageType.BATTERY, fullMessage);
    }

    public void logTransit(String robotName, String zoneInfo, String message) {
        String fullMessage = String.format("%s - %s", zoneInfo, message);
        logMessage(robotName, MessageType.TRANSIT, fullMessage);
    }

    public void logCoordination(String robotName, String message) {
        logMessage(robotName, MessageType.COORDINATION, message);
    }

    public void logDelivery(String robotName, String packageInfo, String message) {
        String fullMessage = String.format("Colis %s - %s", packageInfo, message);
        logMessage(robotName, MessageType.DELIVERY, fullMessage);
    }

    public void logCharging(String robotName, double beforeLevel, double afterLevel, String message) {
        String fullMessage = String.format("Charge: %.1f%% → %.1f%% - %s", beforeLevel, afterLevel, message);
        logMessage(robotName, MessageType.BATTERY, fullMessage);
    }

    public void logError(String robotName, String message) {
        logMessage(robotName, MessageType.ERROR, message);
    }

    // Afficher les statistiques
    public void printStatistics() {
        System.out.println("\n" + BOLD + "=== STATISTIQUES DES MESSAGES ===" + RESET);

        // Statistiques globales
        System.out.println("\nStatistiques globales:");
        for (MessageType type : MessageType.values()) {
            String color = useColors ? type.getColor() : "";
            String reset = useColors ? RESET : "";
            System.out.printf("%s%-12s%s: %d messages\n",
                    color, type.getName(), reset, globalCounts.get(type));
        }

        // Statistiques par robot
        System.out.println("\nStatistiques par robot:");
        for (Map.Entry<String, Map<MessageType, Integer>> entry : robotCounts.entrySet()) {
            String robotName = entry.getKey();
            Map<MessageType, Integer> robotStats = entry.getValue();

            System.out.println("\n" + BOLD + robotName + RESET + ":");
            for (MessageType type : MessageType.values()) {
                String color = useColors ? type.getColor() : "";
                String reset = useColors ? RESET : "";
                System.out.printf("  %s%-12s%s: %d messages\n",
                        color, type.getName(), reset, robotStats.get(type));
            }
        }
    }
}
